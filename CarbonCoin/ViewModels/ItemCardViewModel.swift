//
//  ItemCardViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import Foundation
import SwiftUI

// MARK: ViewModel
// 可观察的卡片视图模型，用于支持图像编辑等功能
class ItemCardViewModel: ObservableObject, Identifiable {
    @Published var itemCard: ItemCard

    var id: UUID { itemCard.id }

    init(itemCard: ItemCard) {
        self.itemCard = itemCard
    }

    // 更新图片并保存到文件系统
    func updateImage(_ newImage: UIImage) {
        guard let data = newImage.jpegData(compressionQuality: 0.9) else { return }

        // 使用原有的文件名或生成新的文件名
        let fileName = itemCard.imageFileName.isEmpty ? "\(itemCard.id.uuidString).jpg" : itemCard.imageFileName
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first!.appendingPathComponent(fileName)

        do {
            try data.write(to: url)
            // 更新模型
            itemCard = ItemCard(
                id: itemCard.id,
                tags: itemCard.tags,
                description: itemCard.description,
                title: itemCard.title,
                imageFileName: fileName,
                createdAt: itemCard.createdAt
            )
        } catch {
            print("保存图片失败: \(error)")
        }
    }
}

// MARK: 卡片存储管理
class CardStore: ObservableObject {
    @Published var cards: [ItemCard] = []
    private let userDefaultsKey = "SavedCards"

    init() {
        loadCards()
    }

    // 保存卡片到 UserDefaults 和图像到文件系统
    func saveCard(tags: [String], description: String, title: String, imageData: Data) {
        let id = UUID()
        let imageFileName = saveImageToDocuments(imageData: imageData, id: id)
        let card = ItemCard(id: id, tags: tags, description: description, title: title, imageFileName: imageFileName, createdAt: Date())
        cards.append(card)
        saveCards()
    }

    // 更新现有卡片
    func updateCard(_ updatedCard: ItemCard) {
        if let index = cards.firstIndex(where: { $0.id == updatedCard.id }) {
            cards[index] = updatedCard
            saveCards()
        }
    }

    // 从 UserDefaults 加载卡片
    private func loadCards() {
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let savedCards = try? JSONDecoder().decode([ItemCard].self, from: data) {
            cards = savedCards
        }
    }

    // 保存卡片到 UserDefaults
    private func saveCards() {
        if let data = try? JSONEncoder().encode(cards) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }

    // 保存图像到 Documents 目录
    private func saveImageToDocuments(imageData: Data, id: UUID) -> String {
        let fileName = "\(id.uuidString).jpg"
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent(fileName)
        try? imageData.write(to: url)
        return fileName // 只返回文件名
    }
}

