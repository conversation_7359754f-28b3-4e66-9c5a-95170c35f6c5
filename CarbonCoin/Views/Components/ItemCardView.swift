//
//  ItemCardView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import SwiftUI

struct ItemCardView: View {
    let card: ItemCard
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 图像
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .clipShape(RoundedRectangle(cornerRadius: 10))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            } else {
                Rectangle()
                    .fill(.gray.opacity(0.2))
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .overlay(
                        Text("Image Unavailable")
                            .foregroundColor(.white)
                            .font(.caption)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 10))
            }
            
            // 标题
            Text(card.title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .padding(.top, 4)
            
            // 标签
            HStack {
                ForEach(card.tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.15))
                        .clipShape(Capsule())
                        .foregroundColor(.blue)
                }
            }
            
            // 描述
            Text(card.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
                .padding(.top, 4)
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .gray.opacity(0.2), radius: 4, x: 0, y: 2)
        .padding(.horizontal, 8)
        .onTapGesture {
            print("Tapped card: \(card.title) (ID: \(card.id))")
        }
    }
}

// MARK: - 卡片缩略图视图
struct ItemCardThumbnailView: View {
    let card: ItemCard

    var body: some View {
        NavigationLink(destination: ItemCardDetailView(card: card)) {
            cardContent
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var cardContent: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            cardImage
            cardTitle
            cardTags
            cardDate
        }
        .padding(Theme.Spacing.sm)
        .background(Color.cardBackground.opacity(0.1))
        .cornerRadius(Theme.CornerRadius.lg)
        .glassCard()
    }

    private var cardImage: some View {
        Group {
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 120)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.md)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 120)
                    .cornerRadius(Theme.CornerRadius.md)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.textSecondary)
                    )
            }
        }
    }

    private var cardTitle: some View {
        Text(card.title)
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    private var cardTags: some View {
        HStack {
            ForEach(Array(card.tags.prefix(2)), id: \.self) { tag in
                Text(tag)
                    .font(.caption2)
                    .foregroundColor(.textPrimary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.brandGreen.opacity(0.2))
                    .cornerRadius(Theme.CornerRadius.sm)
            }

            if card.tags.count > 2 {
                Text("+\(card.tags.count - 2)")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
    }

    private var cardDate: some View {
        Text(card.formattedCreatedAt)
            .font(.caption2)
            .foregroundColor(.textSecondary)
    }
}

// MARK: - 卡片详情视图
struct ItemCardDetailView: View {
    let card: ItemCard
    @Environment(\.dismiss) private var dismiss
    @State private var isCropping = false

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                        // 图像
                        if let image = card.image {
                            Image(uiImage: image)
                                .croppable($isCropping, image: $card.image)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 300)
                                .cornerRadius(Theme.CornerRadius.lg)
                                
                                .glassCard()
                        }

                        // 标题和时间
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text(card.title)
                                .font(.title2Brand)
                                .foregroundColor(.textPrimary)

                            Text("创建于 \(card.formattedCreatedAt)")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 描述
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("描述")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            Text(card.description)
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 标签
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("标签")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 80), spacing: 8)
                            ], spacing: 8) {
                                ForEach(card.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(.captionBrand)
                                        .foregroundColor(.textPrimary)
                                        .padding(.horizontal, Theme.Spacing.sm)
                                        .padding(.vertical, 4)
                                        .background(Color.brandGreen.opacity(0.2))
                                        .cornerRadius(Theme.CornerRadius.sm)
                                }
                            }
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        Spacer(minLength: Theme.Spacing.tab)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("卡片详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .foregroundColor(.brandGreen)
                }
            }
        }
    }
}

struct ItemCardView_Previews: PreviewProvider {
    static let sampleCard = ItemCard(
        id: UUID(),
        tags: ["塑料瓶身", "纸质杯托"],
        description: "这是一个赛百味品牌的杯子。",
        title: "赛百味杯子",
        imageFileName: "",
        createdAt: Date()
    )

    static var previews: some View {
        Group {
            // 原始卡片视图
            ItemCardView(card: sampleCard)
                .previewLayout(.sizeThatFits)
                .padding()
                .background(Color.gray.opacity(0.1))
                .previewDisplayName("Original Card View")

            // 缩略图视图
            ItemCardThumbnailView(card: sampleCard)
                .previewLayout(.sizeThatFits)
                .padding()
                .background(Color.gray.opacity(0.1))
                .previewDisplayName("Thumbnail View")

            // 详情视图
            ItemCardDetailView(card: sampleCard)
                .previewDisplayName("Detail View")
        }
    }
}
